import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Grid,
  IconButton,
  Divider,
  Chip,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  useTheme,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Alert,
  Slider,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Checkbox,
  Menu
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import TodayIcon from '@mui/icons-material/Today';
import PersonIcon from '@mui/icons-material/Person';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import EventIcon from '@mui/icons-material/Event';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import GroupIcon from '@mui/icons-material/Group';
import GroupWorkIcon from '@mui/icons-material/GroupWork';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import { format, addDays, subDays, startOfDay, addHours, isSameDay, getDaysInMonth, getDay } from 'date-fns';
import TaskForm from '../TaskForm';
import CloseIcon from '@mui/icons-material/Close';

const DailyView = ({ selectedDate, events = [], tasks, users, onTaskCreate, onTaskUpdate }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const [currentDate, setCurrentDate] = useState(selectedDate || new Date());
  const [currentTime, setCurrentTime] = useState(new Date());

  // Zoom state - default is 1 (60px per hour)
  const [zoomLevel, setZoomLevel] = useState(1);
  const hourHeight = 60 * zoomLevel; // Base height * zoom level

  // Update currentDate when selectedDate prop changes
  useEffect(() => {
    if (selectedDate) {
      console.log('DailyView - selectedDate changed:', selectedDate);
      // Create a new date object to ensure we're working with the exact date
      const newDate = new Date(selectedDate);
      setCurrentDate(newDate);
    }
  }, [selectedDate]);

  // Log the current date whenever it changes
  useEffect(() => {
    console.log('DailyView - currentDate changed:', currentDate);
  }, [currentDate]);

  // Force update of currentDate when component mounts
  useEffect(() => {
    if (selectedDate) {
      console.log('DailyView - Initial selectedDate:', selectedDate);
      // Create a new date object to ensure we're working with the exact date
      const newDate = new Date(selectedDate);
      setCurrentDate(newDate);
    }
  }, []);
  const [assignees, setAssignees] = useState([]);
  const [showAllAssignees, setShowAllAssignees] = useState(true);
  const [selectedAssignees, setSelectedAssignees] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [openTaskDialog, setOpenTaskDialog] = useState(false);
  const [openMonthlyDialog, setOpenMonthlyDialog] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  const scrollContainerRef = useRef(null);
  const headerScrollRef = useRef(null);

  // Assignee grouping state with localStorage persistence
  const [assigneeGroups, setAssigneeGroups] = useState(() => {
    try {
      const savedGroups = localStorage.getItem('dailyViewAssigneeGroups');
      return savedGroups ? JSON.parse(savedGroups) : [];
    } catch (error) {
      console.error('Error loading assignee groups from localStorage:', error);
      return [];
    }
  });

  const [openGroupDialog, setOpenGroupDialog] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [newGroupName, setNewGroupName] = useState('');
  const [groupMemberIds, setGroupMemberIds] = useState([]);

  const [expandedGroups, setExpandedGroups] = useState(() => {
    try {
      const savedExpandedGroups = localStorage.getItem('dailyViewExpandedGroups');
      return savedExpandedGroups ? JSON.parse(savedExpandedGroups) : {};
    } catch (error) {
      console.error('Error loading expanded groups from localStorage:', error);
      return {};
    }
  });

  // Dragging state
  const [isDragging, setIsDragging] = useState(false);
  const [draggedTask, setDraggedTask] = useState(null);
  const [dragType, setDragType] = useState(null); // 'move', 'resize-top', 'resize-bottom'
  const [dragOffset, setDragOffset] = useState(0);

  // Extract unique assignees from tasks that have tasks on the current day
  useEffect(() => {
    if (tasks && tasks.length > 0) {
      const uniqueAssignees = [];
      const assigneeMap = new Map(); // For user assignees with IDs
      const nonUserAssigneeMap = new Map(); // For non-user assignees (grouped by name)
      const tasksForToday = tasks.filter(task => isTaskOnDay(task, currentDate) && !task.deleted);

      // Always add "Unassigned" column for tasks without assignees
      uniqueAssignees.push({
        _id: 'unassigned',
        name: t('calendar.dailyView.unassigned'),
        email: '',
        isSpecial: true
      });

      // Extract assignees from tasks for today
      tasksForToday.forEach(task => {
        if (task.assignees && task.assignees.length > 0) {
          task.assignees.forEach(assignee => {
            // Handle non-user assignees (they have isNonUserAssignee flag or no _id)
            if (assignee && (assignee.isNonUserAssignee || (!assignee._id && assignee.name))) {
              const name = assignee.name || 'Unknown';

              // Create a unique ID for this non-user assignee based on name
              // If we already have this name, use the existing entry
              if (!nonUserAssigneeMap.has(name)) {
                const nonUserAssignee = {
                  _id: `non-user-${name.replace(/\s+/g, '-').toLowerCase()}`,
                  name: name,
                  email: assignee.email || '',
                  isNonUserAssignee: true,
                  originalAssignee: assignee // Keep reference to original assignee object
                };
                nonUserAssigneeMap.set(name, nonUserAssignee);
                uniqueAssignees.push(nonUserAssignee);
              }
            }
            // Handle regular user assignees with IDs
            else if (assignee && assignee._id && !assigneeMap.has(assignee._id)) {
              assigneeMap.set(assignee._id, true);
              uniqueAssignees.push(assignee);
            }
          });
        }
      });

      console.log('Extracted assignees for calendar:', uniqueAssignees);
      setAssignees(uniqueAssignees);
      setSelectedAssignees(uniqueAssignees.map(a => a._id));
    }
  }, [tasks, currentDate, t]);

  // Get events for the current day
  const getEventsForDay = () => {
    if (!events || events.length === 0) return [];

    return events.filter(event => {
      if (!event.date) return false;
      const eventDate = new Date(event.date);
      return isSameDay(eventDate, currentDate);
    });
  };

  const handlePrevDay = () => {
    const newDate = subDays(currentDate, 1);
    setCurrentDate(newDate);
    // Update URL with the new date
    const formattedDate = newDate.toISOString().split('T')[0];
    navigate(`/calendar/daily/${formattedDate}`);
  };

  const handleNextDay = () => {
    const newDate = addDays(currentDate, 1);
    setCurrentDate(newDate);
    // Update URL with the new date
    const formattedDate = newDate.toISOString().split('T')[0];
    navigate(`/calendar/daily/${formattedDate}`);
  };

  const handleToday = () => {
    const newDate = new Date();
    setCurrentDate(newDate);
    // Update URL with the new date
    const formattedDate = newDate.toISOString().split('T')[0];
    navigate(`/calendar/daily/${formattedDate}`);
  };

  const handleOpenMonthlyDialog = () => {
    setOpenMonthlyDialog(true);
  };

  const handleCloseMonthlyDialog = () => {
    setOpenMonthlyDialog(false);
  };

  // Zoom control functions
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.5, 4)); // Max zoom level: 4x
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.5, 0.5)); // Min zoom level: 0.5x
  };

  const handleResetZoom = () => {
    setZoomLevel(1); // Reset to default zoom
  };

  const handleDateSelect = (day) => {
    const newDate = new Date(currentDate);
    newDate.setDate(day);
    setCurrentDate(newDate);
    setOpenMonthlyDialog(false);

    // Update URL with the new date
    const formattedDate = newDate.toISOString().split('T')[0];
    navigate(`/calendar/daily/${formattedDate}`);
  };

  const handleAssigneeToggle = () => {
    setShowAllAssignees(!showAllAssignees);
    if (!showAllAssignees) {
      // Show all assignees
      setSelectedAssignees(assignees.map(a => a._id));
    }
  };

  const handleAssigneeChange = (event) => {
    setSelectedAssignees(event.target.value);
  };

  // Group management functions
  const handleOpenGroupDialog = (group = null) => {
    setSelectedGroup(group);
    setNewGroupName(group ? group.name : '');
    setGroupMemberIds(group ? group.memberIds : []);
    setOpenGroupDialog(true);
  };

  const handleCloseGroupDialog = () => {
    setOpenGroupDialog(false);
    setSelectedGroup(null);
    setNewGroupName('');
    setGroupMemberIds([]);
  };

  const handleCreateOrUpdateGroup = () => {
    console.log('Creating/updating group with name:', newGroupName);
    console.log('Group members:', groupMemberIds);
    console.log('Selected group:', selectedGroup);

    if (!newGroupName.trim()) {
      setSnackbarMessage(t('calendar.dailyView.groupNameRequired', 'Group name is required'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (groupMemberIds.length < 2) {
      setSnackbarMessage(t('calendar.dailyView.minTwoMembers', 'A group must have at least 2 members'));
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    if (selectedGroup) {
      // Update existing group
      const updatedGroups = assigneeGroups.map(g =>
        g.id === selectedGroup.id
          ? { ...g, name: newGroupName, memberIds: groupMemberIds }
          : g
      );
      setAssigneeGroups(updatedGroups);
    } else {
      // Create new group
      const newGroup = {
        id: `group-${Date.now()}`,
        name: newGroupName,
        memberIds: groupMemberIds
      };

      console.log('Creating new group:', newGroup);

      // Update groups state
      const updatedGroups = [...assigneeGroups, newGroup];
      setAssigneeGroups(updatedGroups);

      // Auto-expand new group by default
      const newExpandedGroups = {
        ...expandedGroups,
        [newGroup.id]: false // Start collapsed by default
      };

      console.log('Setting expanded groups:', newExpandedGroups);
      setExpandedGroups(newExpandedGroups);
    }

    handleCloseGroupDialog();

    // Show success message
    setSnackbarMessage(
      selectedGroup
        ? t('calendar.dailyView.groupUpdated', 'Group updated successfully')
        : t('calendar.dailyView.groupCreated', 'Group created successfully')
    );
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
  };

  const handleDeleteGroup = (groupId) => {
    const updatedGroups = assigneeGroups.filter(g => g.id !== groupId);
    setAssigneeGroups(updatedGroups);

    // Remove from expanded groups
    const newExpandedGroups = { ...expandedGroups };
    delete newExpandedGroups[groupId];
    setExpandedGroups(newExpandedGroups);

    // Show success message
    setSnackbarMessage(t('calendar.dailyView.groupDeleted', 'Group deleted successfully'));
    setSnackbarSeverity('success');
    setSnackbarOpen(true);
  };

  const handleToggleGroupExpansion = (groupId) => {
    setExpandedGroups({
      ...expandedGroups,
      [groupId]: !expandedGroups[groupId]
    });
  };

  // Get assignees for display, handling groups
  const getDisplayAssignees = () => {
    if (!assignees || assignees.length === 0) return [];

    // Start with non-group assignees
    let displayAssignees = [];

    // Keep track of which assignees are part of expanded groups
    const expandedGroupMembers = new Set();

    // First, identify all members of expanded groups
    assigneeGroups.forEach(group => {
      const isExpanded = expandedGroups[group.id];
      if (isExpanded) {
        group.memberIds.forEach(memberId => {
          expandedGroupMembers.add(memberId);
        });
      }
    });

    // Add all non-group assignees that aren't part of expanded groups
    assignees.forEach(assignee => {
      // Skip assignees that are part of expanded groups (they'll be added later)
      if (!expandedGroupMembers.has(assignee._id)) {
        displayAssignees.push(assignee);
      }
    });

    console.log('getDisplayAssignees - Initial non-group assignees:', displayAssignees);
    console.log('getDisplayAssignees - Groups:', assigneeGroups);
    console.log('getDisplayAssignees - Expanded groups:', expandedGroups);

    // Now process each group
    const groupsWithMembers = [];

    assigneeGroups.forEach(group => {
      const isExpanded = expandedGroups[group.id];
      console.log(`Processing group ${group.name} (${group.id}), expanded: ${isExpanded}`);
      console.log('Group members:', group.memberIds);

      // Create the group assignee object
      const groupAssignee = {
        _id: group.id,
        name: group.name,
        isGroup: true,
        memberIds: group.memberIds,
        members: assignees.filter(a => group.memberIds.includes(a._id)),
        isExpanded: isExpanded
      };

      if (!isExpanded) {
        // For collapsed groups, just add the group
        console.log('Adding collapsed group assignee:', groupAssignee);
        displayAssignees.push(groupAssignee);
      } else {
        // For expanded groups, we'll add the group and its members together
        const groupMembers = assignees.filter(a => group.memberIds.includes(a._id));

        // Store the group and its members to add them in the correct order later
        groupsWithMembers.push({
          group: groupAssignee,
          members: groupMembers
        });
      }
    });

    // Add all expanded groups and their members at the end
    // This ensures they stay together and in the right order
    groupsWithMembers.forEach(({ group, members }) => {
      // Add the group first
      displayAssignees.push(group);

      // Then add all its members with a reference to their group
      members.forEach(member => {
        // Add group reference to the member
        displayAssignees.push({
          ...member,
          groupId: group._id,
          groupName: group.name,
          isGroupMember: true
        });
      });
    });

    console.log('Final display assignees:', displayAssignees);
    return displayAssignees;
  };

  // Generate time slots for the day (hourly)
  const generateTimeSlots = () => {
    const slots = [];
    const dayStart = startOfDay(currentDate);

    for (let i = 0; i < 24; i++) {
      slots.push(addHours(dayStart, i));
    }

    return slots;
  };

  // Organize tasks into a hierarchy (parent tasks and subtasks)
  const organizeTaskHierarchy = (tasks) => {
    // Filter out deleted tasks first
    const nonDeletedTasks = tasks.filter(task => !task.deleted);

    // Separate top-level tasks and subtasks
    const topLevelTasks = nonDeletedTasks.filter(task => !task.parentTask);
    const subtasks = nonDeletedTasks.filter(task => task.parentTask);

    // Add subtasks to their parent tasks
    const tasksWithSubtasks = topLevelTasks.map(task => {
      const taskSubtasks = subtasks.filter(subtask =>
        subtask.parentTask === task._id
      );

      return {
        ...task,
        subtasks: taskSubtasks
      };
    });

    return tasksWithSubtasks;
  };

  // Get tasks for a specific assignee for the entire day
  const getTasksForAssignee = (assigneeId) => {
    // Get all tasks that match the criteria
    const assigneeTasks = tasks.filter(task => {
      // Skip deleted tasks
      if (task.deleted) return false;

      // Skip subtasks (they'll be handled with their parent tasks)
      if (task.parentTask) return false;

      // Handle unassigned tasks
      if (assigneeId === 'unassigned' && (!task.assignees || task.assignees.length === 0)) {
        return isTaskOnDay(task, currentDate);
      }

      // Handle group assignees
      if (assigneeId.startsWith('group-')) {
        // Find the group
        const group = assigneeGroups.find(g => g.id === assigneeId);
        if (group && task.assignees) {
          // Check if any assignee in this task is a member of this group
          return task.assignees.some(assignee => {
            // Get the assignee ID
            const assigneeId = assignee._id ||
                              (assignee.isNonUserAssignee ?
                                `non-user-${assignee.name.replace(/\s+/g, '-').toLowerCase()}` :
                                null);

            // Check if this assignee is in the group
            return assigneeId && group.memberIds.includes(assigneeId) && isTaskOnDay(task, currentDate);
          });
        }
        return false;
      }

      // If this is an expanded group header, don't show any tasks directly on it
      const assignee = displayAssignees.find(a => a._id === assigneeId);
      if (assignee && assignee.isGroup && assignee.isExpanded) {
        return false;
      }

      // Handle non-user assignees (they have IDs like "non-user-name")
      if (assigneeId.startsWith('non-user-') && task.assignees) {
        // Extract the name from the ID (e.g., "non-user-john-doe" -> "john-doe")
        const nameFromId = assigneeId.replace('non-user-', '');

        // Check if any assignee in this task matches this non-user name
        return task.assignees.some(assignee => {
          // Check if this is a non-user assignee with matching name
          if (assignee && (assignee.isNonUserAssignee || (!assignee._id && assignee.name))) {
            const assigneeName = assignee.name || '';
            const normalizedName = assigneeName.replace(/\s+/g, '-').toLowerCase();
            return normalizedName === nameFromId && isTaskOnDay(task, currentDate);
          }
          return false;
        });
      }

      // Handle regular user assignees with IDs
      return task.assignees &&
             task.assignees.some(assignee => assignee._id === assigneeId) &&
             isTaskOnDay(task, currentDate);
    });

    // Organize tasks with their subtasks
    return organizeTaskHierarchy(assigneeTasks);
  };

  // Check if a specific day has any tasks
  const dayHasTasks = (day) => {
    if (!tasks || tasks.length === 0) return false;

    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);

    return tasks.some(task => {
      if (task.deleted) return false;
      return isTaskOnDay(task, date);
    });
  };

  // Render the mini monthly calendar
  const renderMiniCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(new Date(year, month));
    const firstDayOfMonth = getDay(new Date(year, month, 1));
    const currentDay = currentDate.getDate();
    const today = new Date();
    const isCurrentMonth = today.getMonth() === month && today.getFullYear() === year;
    const todayDate = today.getDate();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(
        <Grid item key={`empty-${i}`} sx={{ width: '14.28%', p: 0.5 }}>
          <Box sx={{ height: 30 }} />
        </Grid>
      );
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const isToday = isCurrentMonth && day === todayDate;
      const isSelected = day === currentDay;
      const hasTask = dayHasTasks(day);

      days.push(
        <Grid item key={day} sx={{ width: '14.28%', p: 0.5 }}>
          <Box
            onClick={() => handleDateSelect(day)}
            sx={{
              height: 30,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '50%',
              cursor: 'pointer',
              bgcolor: isSelected ? 'primary.main' : isToday ? 'primary.light' : 'transparent',
              color: isSelected ? 'primary.contrastText' : isToday ? 'primary.contrastText' : 'text.primary',
              position: 'relative',
              '&:hover': {
                bgcolor: !isSelected ? 'action.hover' : 'primary.main'
              }
            }}
          >
            <Typography variant="caption" fontWeight={isToday || isSelected ? 'bold' : 'normal'}>
              {day}
            </Typography>

            {hasTask && (
              <Box
                sx={{
                  width: 4,
                  height: 4,
                  borderRadius: '50%',
                  bgcolor: isSelected ? 'primary.contrastText' : 'primary.main',
                  position: 'absolute',
                  bottom: 2
                }}
              />
            )}
          </Box>
        </Grid>
      );
    }

    return days;
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Check if a task falls on the specified day
  const isTaskOnDay = (task, date = currentDate) => {
    // Check if the task has a start time on this day
    if (task.startTime) {
      const taskStart = new Date(task.startTime);
      return isSameDay(taskStart, date);
    }

    // Check if the task has a soft deadline on this day
    if (task.softDeadline) {
      const deadline = new Date(task.softDeadline);
      return isSameDay(deadline, date);
    }

    // Check if the task has a hard deadline on this day
    if (task.hardDeadline) {
      const deadline = new Date(task.hardDeadline);
      return isSameDay(deadline, date);
    }

    return false;
  };

  // Get color for task based on status or deadline type
  const getTaskColor = (task) => {
    // First check status
    switch (task.status) {
      case 'Completed':
        return theme.palette.success.main;
      case 'In Progress':
        return theme.palette.info.main;
      case 'Delayed':
        return theme.palette.warning.main;
      case 'Cancelled':
        return theme.palette.error.main;
      default:
        // If no specific status, check deadline type
        if (task.startTime && isSameDay(new Date(task.startTime), currentDate)) {
          return theme.palette.success.light;
        }
        if (task.hardDeadline && isSameDay(new Date(task.hardDeadline), currentDate)) {
          return theme.palette.error.light;
        }
        if (task.softDeadline && isSameDay(new Date(task.softDeadline), currentDate)) {
          return theme.palette.primary.light;
        }
        return theme.palette.grey[300];
    }
  };

  // Render a task card
  const renderTaskCard = (task) => {
    const taskColor = getTaskColor(task);

    // Determine what time to display
    let timeDisplay = '';
    let timeType = '';

    if (task.startTime && isSameDay(new Date(task.startTime), currentDate)) {
      timeDisplay = format(new Date(task.startTime), 'h:mm a');
      timeType = t('calendar.dailyView.startTime');
    } else if (task.softDeadline && isSameDay(new Date(task.softDeadline), currentDate)) {
      timeDisplay = format(new Date(task.softDeadline), 'h:mm a');
      timeType = t('calendar.dailyView.softDeadline');
    } else if (task.hardDeadline && isSameDay(new Date(task.hardDeadline), currentDate)) {
      timeDisplay = format(new Date(task.hardDeadline), 'h:mm a');
      timeType = t('calendar.dailyView.hardDeadline');
    }

    return (
      <Paper
        key={task._id}
        sx={{
          p: 1,
          mb: 1,
          borderLeft: `4px solid ${taskColor}`,
          backgroundColor: `${taskColor}22` // Add transparency
        }}
      >
        <Typography variant="subtitle2" noWrap>{task.name}</Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
          <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
          <Typography variant="caption">
            {timeType}: {timeDisplay}
          </Typography>
        </Box>
        {task.location && (
          <Typography variant="caption" sx={{ display: 'block' }}>
            {t('calendar.dailyView.location')}: {task.location}
          </Typography>
        )}

        {/* Subtasks */}
        {task.subtasks && task.subtasks.length > 0 && (
          <Box sx={{ mt: 1, pt: 1, borderTop: '1px dashed', borderColor: 'divider' }}>
            <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
              {t('calendar.dailyView.subtasks', 'Subtasks')}:
            </Typography>
            {task.subtasks.map(subtask => (
              <Box
                key={subtask._id}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 0.5,
                  p: 0.5,
                  borderRadius: 0.5,
                  backgroundColor: 'rgba(0,0,0,0.03)'
                }}
              >
                <Typography variant="caption" noWrap sx={{ maxWidth: '80%' }}>
                  {subtask.name}
                </Typography>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: getTaskColor(subtask)
                  }}
                />
              </Box>
            ))}
          </Box>
        )}
      </Paper>
    );
  };

  // Get assignees for display with group handling
  const displayAssignees = getDisplayAssignees();

  // Filter assignees based on selection
  const filteredAssignees = showAllAssignees
    ? displayAssignees
    : displayAssignees.filter(a => selectedAssignees.includes(a._id));

  const timeSlots = generateTimeSlots();
  const todayEvents = getEventsForDay();

  // This function is now directly implemented in the onClick handler

  // Handle mouse down on task for dragging
  const handleTaskMouseDown = (task, event, type = 'move') => {
    // Prevent default to avoid text selection during drag
    event.preventDefault();
    event.stopPropagation();

    // Find the task element
    const taskElement = event.currentTarget;
    const rect = taskElement.getBoundingClientRect();

    // Store initial positions
    const initialMouseY = event.clientY;

    // Get the exact position from the DOM element's computed style
    // This ensures we're using the actual rendered position
    const computedStyle = window.getComputedStyle(taskElement);
    const initialTop = parseInt(computedStyle.top, 10);
    const initialHeight = parseInt(computedStyle.height, 10);

    // Calculate the offset within the task element where the mouse was clicked
    const offsetY = initialMouseY - rect.top;

    // Set dragging state
    setIsDragging(true);
    setDraggedTask({
      ...task,
      _initialMouseY: initialMouseY,
      _initialTop: initialTop,
      _initialHeight: initialHeight,
      _offsetY: offsetY,
      _justDragged: true
    });
    setDragType(type);
    setDragOffset(offsetY);

    // Log for debugging
    console.log('Drag start:', {
      type,
      initialMouseY,
      initialTop,
      initialHeight,
      offsetY,
      rectTop: rect.top,
      rectHeight: rect.height
    });
  };

  // Handle mouse move for dragging
  const handleMouseMove = (event) => {
    if (!isDragging || !draggedTask) return;

    // Get the current mouse position
    const clientY = event.clientY;
    const clientX = event.clientX;

    // Find all task elements with the same task ID (for multiple assignees)
    const taskElements = document.querySelectorAll(`[data-task-id="${draggedTask._id}"]`);
    if (taskElements.length === 0) return;

    // Calculate the delta movement from the initial mouse position
    const deltaY = clientY - draggedTask._initialMouseY;

    // Get the current position and size
    let newTop, newHeight;

    if (dragType === 'move') {
      // Apply the new position based on the initial position plus the delta
      newTop = draggedTask._initialTop + deltaY;
      newHeight = draggedTask._initialHeight;

      // Update all instances of this task across assignee columns
      taskElements.forEach(element => {
        element.style.top = `${newTop}px`;
      });
    } else if (dragType === 'resize-top') {
      // For resizing from the top, calculate new position and height
      newTop = draggedTask._initialTop + deltaY;
      newHeight = draggedTask._initialHeight - deltaY;

      // Ensure minimum height
      if (newHeight >= 30) {
        // Update all instances of this task across assignee columns
        taskElements.forEach(element => {
          element.style.top = `${newTop}px`;
          element.style.height = `${newHeight}px`;
        });
      } else {
        newTop = draggedTask._initialTop + (draggedTask._initialHeight - 30);
        newHeight = 30;

        // Update all instances of this task across assignee columns
        taskElements.forEach(element => {
          element.style.top = `${newTop}px`;
          element.style.height = `${newHeight}px`;
        });
      }
    } else if (dragType === 'resize-bottom') {
      // For resizing from the bottom, calculate new height
      newTop = draggedTask._initialTop;
      newHeight = draggedTask._initialHeight + deltaY;

      // Ensure minimum height
      if (newHeight >= 30) {
        // Update all instances of this task across assignee columns
        taskElements.forEach(element => {
          element.style.height = `${newHeight}px`;
        });
      } else {
        newHeight = 30;
        // Update all instances of this task across assignee columns
        taskElements.forEach(element => {
          element.style.height = `${newHeight}px`;
        });
      }
    }

    // Calculate times based on position and height, accounting for zoom level
    const adjustedTop = newTop / zoomLevel;
    const adjustedHeight = newHeight / zoomLevel;

    const startHour = Math.floor(adjustedTop / 60);
    const startMinute = Math.floor(adjustedTop % 60);

    const endHour = Math.floor((adjustedTop + adjustedHeight) / 60);
    const endMinute = Math.floor((adjustedTop + adjustedHeight) % 60);

    // Format times for display
    const startTimeStr = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`;
    const endTimeStr = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

    // Calculate duration
    const durationMinutes = adjustedHeight;
    const durationHours = Math.floor(durationMinutes / 60);
    const remainingMinutes = Math.floor(durationMinutes % 60);
    const durationStr = `${durationHours}h ${remainingMinutes}m`;

    // Update drag info based on drag type
    let infoText = '';
    if (dragType === 'move') {
      infoText = `${t('calendar.dailyView.dragInfo.start', { time: startTimeStr })} | ${t('calendar.dailyView.dragInfo.end', { time: endTimeStr })}`;
    } else if (dragType === 'resize-top') {
      infoText = `${t('calendar.dailyView.dragInfo.start', { time: startTimeStr })} | ${t('calendar.dailyView.dragInfo.duration', { duration: durationStr })}`;
    } else if (dragType === 'resize-bottom') {
      infoText = `${t('calendar.dailyView.dragInfo.end', { time: endTimeStr })} | ${t('calendar.dailyView.dragInfo.duration', { duration: durationStr })}`;
    }

    // Update drag info state
    setDragInfo({
      text: infoText,
      x: clientX + 15, // Offset from cursor
      y: clientY + 15
    });
  };

  // Track if we just finished dragging to prevent dialog from opening
  const [, setJustFinishedDragging] = useState(false);

  // State for drag info tooltip
  const [dragInfo, setDragInfo] = useState(null);

  // Handle mouse up to complete dragging
  const handleMouseUp = () => {
    if (!isDragging || !draggedTask) {
      setIsDragging(false);
      setDraggedTask(null);
      setDragType(null);
      setDragInfo(null);
      return;
    }

    // Set flag to prevent dialog from opening
    setJustFinishedDragging(true);

    // Clear the drag info
    setDragInfo(null);

    // No need to clear click flags anymore

    // Clear the flag after a short delay
    setTimeout(() => {
      setJustFinishedDragging(false);
    }, 300);

    // Get the first task element to read its current position
    // We can use any of the task elements since they all have the same position/height now
    const taskElement = document.querySelector(`[data-task-id="${draggedTask._id}"]`);
    if (!taskElement) {
      setIsDragging(false);
      setDraggedTask(null);
      setDragType(null);
      return;
    }

    // Create a clean copy of the task without our temporary properties
    const cleanTask = { ...draggedTask };
    delete cleanTask._initialMouseY;
    delete cleanTask._initialTop;
    delete cleanTask._initialHeight;
    delete cleanTask._offsetY;
    // Keep _justDragged flag to prevent dialog from opening

    // Get the current position and size
    const top = parseInt(taskElement.style.top || '0', 10);
    const height = parseInt(taskElement.style.height || '60', 10);

    // Convert position to time - ensure we're getting valid values
    // Account for zoom level and ensure top is within the valid range (0 to 1439 minutes in a day)
    const adjustedTop = top / zoomLevel;
    const clampedTop = Math.max(0, Math.min(adjustedTop, 1439));
    const startHour = Math.floor(clampedTop / 60);
    const startMinute = Math.floor(clampedTop % 60);

    // Create new start time
    const newStartTime = new Date(currentDate);
    newStartTime.setHours(startHour, startMinute, 0, 0);

    // Create updated task data
    const updatedTask = { ...cleanTask };
    updatedTask.startTime = newStartTime;

    // Handle end time based on drag type
    if (dragType === 'move' || dragType === 'resize-bottom') {
      // Calculate end time based on height, accounting for zoom level
      const adjustedHeight = height / zoomLevel;
      const totalMinutes = clampedTop + adjustedHeight;
      const clampedTotalMinutes = Math.max(clampedTop + 30, Math.min(totalMinutes, 1439)); // Ensure at least 30 min duration
      const endHour = Math.floor(clampedTotalMinutes / 60);
      const endMinute = Math.floor(clampedTotalMinutes % 60);

      const newEndTime = new Date(currentDate);
      newEndTime.setHours(endHour, endMinute, 0, 0);

      // Calculate duration in hours:minutes:seconds format
      const durationMs = newEndTime - newStartTime;
      const durationHours = Math.floor(durationMs / (1000 * 60 * 60));
      const durationMinutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      const durationSeconds = Math.floor((durationMs % (1000 * 60)) / 1000);

      updatedTask.duration = `${durationHours.toString().padStart(2, '0')}:${durationMinutes.toString().padStart(2, '0')}:${durationSeconds.toString().padStart(2, '0')}`;

      // Update soft deadline if it exists
      if (updatedTask.softDeadline) {
        updatedTask.softDeadline = newEndTime;
      }
    }

    // Update the task in the database
    if (typeof onTaskUpdate === 'function') {
      onTaskUpdate(updatedTask)
        .then(() => {
          // Show success message
          setSnackbarMessage(t('calendar.dailyView.taskCreatedSuccess'));
          setSnackbarSeverity('success');
          setSnackbarOpen(true);

          // After a short delay, clear the _justDragged flag
          setTimeout(() => {
            if (draggedTask) {
              const cleanedTask = {...draggedTask};
              delete cleanedTask._justDragged;
              // We don't need to update the state here as the task will be refreshed on next render
            }
          }, 300); // 300ms delay to prevent accidental clicks
        })
        .catch(error => {
          // Show error message
          setSnackbarMessage(t('calendar.dailyView.failedToCreateTask', { message: error.message || 'Unknown error' }));
          setSnackbarSeverity('error');
          setSnackbarOpen(true);
        });
    }

    // Reset dragging state
    setIsDragging(false);
    setDraggedTask(null);
    setDragType(null);
  };

  // Handle creating a new task
  const handleCreateTask = (assigneeId, hour) => {
    let assignee = null;

    // Handle different types of assignees
    if (assigneeId !== 'unassigned') {
      // For group assignees
      if (assigneeId.startsWith('group-')) {
        // Find the group
        const group = assigneeGroups.find(g => g.id === assigneeId);
        if (group) {
          // For groups, we'll create a task with all group members as assignees
          const groupAssignees = [];

          // Get all assignees that are part of this group
          group.memberIds.forEach(memberId => {
            if (memberId.startsWith('non-user-')) {
              // Handle non-user assignees in the group
              const nonUserAssignee = assignees.find(a => a._id === memberId);
              if (nonUserAssignee) {
                if (nonUserAssignee.originalAssignee) {
                  groupAssignees.push(nonUserAssignee.originalAssignee);
                } else {
                  groupAssignees.push({
                    name: nonUserAssignee.name,
                    isNonUserAssignee: true
                  });
                }
              }
            } else {
              // Handle regular user assignees in the group
              const userAssignee = users && users.find(user => user._id === memberId);
              if (userAssignee) {
                groupAssignees.push(userAssignee);
              }
            }
          });

          // Create a task with all group members as assignees
          const newTask = {
            name: '',
            taskType: 'Other', // Default to 'Other' type
            status: 'Not Started',
            assignees: groupAssignees,
            startTime: hour,
            softDeadline: addHours(hour, 1),
            dependencies: []
          };

          setSelectedTask(newTask);
          setOpenTaskDialog(true);
          return; // Skip the rest of the function
        }
      }
      // For non-user assignees (they have IDs like "non-user-name")
      else if (assigneeId.startsWith('non-user-')) {
        // Find the non-user assignee in our assignees list
        const nonUserAssignee = assignees.find(a => a._id === assigneeId);
        if (nonUserAssignee) {
          // Use the original assignee object if available
          if (nonUserAssignee.originalAssignee) {
            assignee = nonUserAssignee.originalAssignee;
          } else {
            // Create a new non-user assignee object based on the name
            assignee = {
              name: nonUserAssignee.name,
              isNonUserAssignee: true
            };
          }
        }
      } else {
        // For regular user assignees with IDs
        assignee = (users && users.find(user => user._id === assigneeId)) || null;
      }
    }

    const newTask = {
      name: '',
      taskType: 'Other', // Default to 'Other' type
      status: 'Not Started',
      assignees: assignee ? [assignee] : [],
      startTime: hour,
      softDeadline: addHours(hour, 1),
      dependencies: []
    };

    setSelectedTask(newTask);
    setOpenTaskDialog(true);
  };

  // Handle task form submission
  const handleTaskSubmit = (taskData) => {
    console.log('handleTaskSubmit called with data:', JSON.stringify(taskData, null, 2));
    console.log('selectedTask:', selectedTask ? JSON.stringify(selectedTask, null, 2) : 'null');

    try {
      if (selectedTask && selectedTask._id) {
        console.log('Updating existing task with ID:', selectedTask._id);

        // Ensure the task ID is included in the data
        if (!taskData._id) {
          console.log('Adding _id to taskData for update');
          taskData._id = selectedTask._id;
        }

        // Ensure we're using the correct HTTP method for updates
        console.log('This should trigger a PUT request to /tasks/' + taskData._id);

        // Update existing task
        if (typeof onTaskUpdate === 'function') {
          console.log('Calling onTaskUpdate with data:', JSON.stringify(taskData, null, 2));
          onTaskUpdate(taskData)
            .then(() => {
              // Show success message
              setSnackbarMessage(t('calendar.dailyView.taskCreatedSuccess'));
              setSnackbarSeverity('success');
              setSnackbarOpen(true);

              // Close dialog
              setOpenTaskDialog(false);
              setSelectedTask(null);
            })
            .catch(error => {
              // Show error message
              setSnackbarMessage(t('calendar.dailyView.failedToCreateTask', { message: error.message || 'Unknown error' }));
              setSnackbarSeverity('error');
              setSnackbarOpen(true);
            });
        } else {
          console.error('onTaskUpdate function is not provided');
          setOpenTaskDialog(false);
          setSelectedTask(null);
        }
      } else {
        console.log('Creating new task');
        // Create new task
        if (typeof onTaskCreate === 'function') {
          console.log('Calling onTaskCreate with data:', JSON.stringify(taskData, null, 2));
          onTaskCreate(taskData)
            .then(() => {
              // Show success message
              setSnackbarMessage(t('calendar.dailyView.taskCreatedSuccess'));
              setSnackbarSeverity('success');
              setSnackbarOpen(true);

              // Close dialog immediately
              setOpenTaskDialog(false);
              setSelectedTask(null);
            })
            .catch(error => {
              // Show error message
              setSnackbarMessage(t('calendar.dailyView.failedToCreateTask', { message: error.message || 'Unknown error' }));
              setSnackbarSeverity('error');
              setSnackbarOpen(true);
            });
        } else {
          console.error('onTaskCreate function is not provided');
          // Fallback: Try to use onTaskUpdate if available
          if (typeof onTaskUpdate === 'function') {
            console.log('Falling back to onTaskUpdate for new task');
            onTaskUpdate(taskData)
              .then(() => {
                // Show success message
                setSnackbarMessage(t('calendar.dailyView.taskCreatedSuccess'));
                setSnackbarSeverity('success');
                setSnackbarOpen(true);

                // Close dialog
                setOpenTaskDialog(false);
                setSelectedTask(null);
              })
              .catch(error => {
                // Show error message
                setSnackbarMessage(t('calendar.dailyView.failedToCreateTask', { message: error.message || 'Unknown error' }));
                setSnackbarSeverity('error');
                setSnackbarOpen(true);
              });
          } else {
            console.error('Neither onTaskCreate nor onTaskUpdate functions are provided');
            setOpenTaskDialog(false);
            setSelectedTask(null);
          }
        }
      }
    } catch (error) {
      console.error('Error in handleTaskSubmit:', error);
      // Show error message
      setSnackbarMessage(`An error occurred: ${error.message || 'Unknown error'}`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);

      // Close dialog
      setOpenTaskDialog(false);
      setSelectedTask(null);
    }
  };

  // Update current time every 5 minutes
  useEffect(() => {
    // Function to update time
    const updateTime = () => {
      console.log('Updating current time indicator');
      setCurrentTime(new Date());
    };

    // Set initial time
    updateTime();

    // Update every 5 minutes (300000 ms)
    const intervalId = setInterval(updateTime, 300000);

    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, []);

  // Save assignee groups to localStorage when they change
  useEffect(() => {
    try {
      localStorage.setItem('dailyViewAssigneeGroups', JSON.stringify(assigneeGroups));
    } catch (error) {
      console.error('Error saving assignee groups to localStorage:', error);
    }
  }, [assigneeGroups]);

  // Save expanded groups state to localStorage when it changes
  useEffect(() => {
    try {
      localStorage.setItem('dailyViewExpandedGroups', JSON.stringify(expandedGroups));
    } catch (error) {
      console.error('Error saving expanded groups to localStorage:', error);
    }
  }, [expandedGroups]);

  // Calculate position for current time indicator
  const getCurrentTimePosition = () => {
    // Use currentTime state to ensure re-renders when it updates
    const now = currentTime;

    // Only show the line if we're viewing today
    if (!isSameDay(now, currentDate)) {
      return null;
    }

    const hours = now.getHours();
    const minutes = now.getMinutes();
    const totalMinutes = (hours * 60) + minutes;

    // Position is based on minutes from start of day
    // Each hour is hourHeight pixels tall
    return totalMinutes * (hourHeight / 60);
  };

  // Scroll to current time on initial render and set up scroll synchronization
  useEffect(() => {
    if (scrollContainerRef.current) {
      const now = new Date();
      const currentHour = now.getHours();
      const scrollPosition = currentHour * hourHeight; // Use dynamic hourHeight
      scrollContainerRef.current.scrollTop = scrollPosition;

      // Set up horizontal scroll synchronization between header and content
      const syncHeaderScroll = () => {
        if (headerScrollRef.current) {
          headerScrollRef.current.scrollLeft = scrollContainerRef.current.scrollLeft;
        }
      };

      const syncContentScroll = () => {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.scrollLeft = headerScrollRef.current.scrollLeft;
        }
      };

      // Add scroll event listeners
      scrollContainerRef.current.addEventListener('scroll', syncHeaderScroll);
      if (headerScrollRef.current) {
        headerScrollRef.current.addEventListener('scroll', syncContentScroll);
      }

      // Clean up event listeners
      return () => {
        if (scrollContainerRef.current) {
          scrollContainerRef.current.removeEventListener('scroll', syncHeaderScroll);
        }
        if (headerScrollRef.current) {
          headerScrollRef.current.removeEventListener('scroll', syncContentScroll);
        }
      };
    }
  }, [hourHeight]); // Re-run when hourHeight changes due to zoom

  // Add keyboard event listener for left/right arrow navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowLeft') {
        handlePrevDay();
      } else if (e.key === 'ArrowRight') {
        handleNextDay();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Get task position and height based on its time and duration
  const getTaskPosition = (task, overlappingTasks = []) => {
    const startTime = task.startTime ? new Date(task.startTime) : null;

    if (!startTime) return { top: 0, height: hourHeight, left: 4, width: 'calc(100% - 8px)' };

    const startHour = startTime.getHours();
    const startMinutes = startTime.getMinutes();
    const top = (startHour * 60 + startMinutes) * zoomLevel; // Apply zoom to top position

    let height = hourHeight; // Default 1 hour with zoom applied

    // Calculate height based on duration if available
    if (task.duration && task.duration !== '00:00:00') {
      try {
        // Destructure only what we need, ignoring seconds
        const [hours, minutes] = task.duration.split(':').map(Number);
        // Convert duration to minutes for height calculation (1 minute = 1 pixel * zoom)
        height = ((hours * 60) + minutes) * zoomLevel;

        // Minimum height with zoom applied
        const minHeight = 30 * zoomLevel;
        if (height < minHeight) height = minHeight;
      } catch (error) {
        console.error('Error calculating task height from duration:', error);
        // Fallback to default height
        height = hourHeight;
      }
    }
    // If no duration but has end time, calculate height from end time
    else if (task.softDeadline || task.hardDeadline) {
      const endTime = task.softDeadline ? new Date(task.softDeadline) :
                    (task.hardDeadline ? new Date(task.hardDeadline) : null);

      if (endTime) {
        const endHour = endTime.getHours();
        const endMinutes = endTime.getMinutes();
        const endPosition = (endHour * 60 + endMinutes) * zoomLevel;
        height = endPosition - top;

        // Minimum height with zoom applied
        const minHeight = 30 * zoomLevel;
        if (height < minHeight) height = minHeight;
      }
    }

    // Calculate horizontal position for overlapping tasks
    let left = 4;
    let width = 'calc(100% - 8px)';

    if (overlappingTasks.length > 0) {
      const totalOverlapping = overlappingTasks.length + 1; // +1 for current task
      const index = overlappingTasks.findIndex(t => t._id === task._id);
      const position = index === -1 ? overlappingTasks.length : index;

      // Calculate width and left position based on number of overlapping tasks
      const segmentWidth = (100 - 8) / totalOverlapping; // 8px for padding (4px on each side)
      width = `${segmentWidth}%`;
      left = 4 + (position * segmentWidth);
    }

    return { top, height, left, width };
  };

  return (
    <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header with date navigation */}
      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}`, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handlePrevDay} size="small">
            <ArrowBackIcon />
          </IconButton>
          <Typography
            variant="h6"
            sx={{
              mx: 2,
              cursor: 'pointer',
              '&:hover': { textDecoration: 'underline' }
            }}
            onClick={handleOpenMonthlyDialog}
          >
            {format(currentDate, 'EEEE, MMMM d, yyyy')}
          </Typography>
          <IconButton onClick={handleNextDay} size="small">
            <ArrowForwardIcon />
          </IconButton>
          <Button
            startIcon={<TodayIcon />}
            onClick={handleToday}
            size="small"
            sx={{ ml: 2 }}
          >
            {t('calendar.today', 'Today')}
          </Button>

          {/* Zoom controls */}
          <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title={t('calendar.dailyView.zoomOut', 'Zoom Out')} leaveDelay={0} disableInteractive>
              <IconButton onClick={handleZoomOut} size="small" disabled={zoomLevel <= 0.5}>
                <ZoomOutIcon />
              </IconButton>
            </Tooltip>
            <Typography variant="body2" sx={{ mx: 1, minWidth: '45px', textAlign: 'center' }}>
              {Math.round(zoomLevel * 100)}%
            </Typography>
            <Tooltip title={t('calendar.dailyView.zoomIn', 'Zoom In')} leaveDelay={0} disableInteractive>
              <IconButton onClick={handleZoomIn} size="small" disabled={zoomLevel >= 4}>
                <ZoomInIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title={t('calendar.dailyView.resetZoom', 'Reset Zoom')} leaveDelay={0} disableInteractive>
              <IconButton onClick={handleResetZoom} size="small" disabled={zoomLevel === 1}>
                <RestartAltIcon />
              </IconButton>
            </Tooltip>
          </Box>

          {/* Group management button */}
          <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
          <Tooltip title={t('calendar.dailyView.manageGroups', 'Manage Assignee Groups')} leaveDelay={0}>
            <Button
              startIcon={<GroupWorkIcon />}
              onClick={() => handleOpenGroupDialog()}
              size="small"
              color="primary"
              variant="outlined"
            >
              {t('calendar.dailyView.groups', 'Groups')}
            </Button>
          </Tooltip>
        </Box>

        {/* Event display */}
        {todayEvents.length > 0 && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EventIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            <Typography variant="subtitle1">
              {todayEvents.map(event => event.name).join(', ')}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Sticky header for assignees */}
      <Box
        ref={headerScrollRef}
        sx={{
          display: 'flex',
          borderBottom: `1px solid ${theme.palette.divider}`,
          position: 'sticky',
          top: 0,
          zIndex: 10,
          backgroundColor: theme.palette.background.paper,
          overflowX: 'auto', // Enable horizontal scrolling
          '&::-webkit-scrollbar': {
            height: 8,
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.divider,
            borderRadius: 4,
          }
        }}>
        {/* Time column header */}
        <Box sx={{
          width: 80,
          minWidth: 80, // Ensure minimum width
          p: 1,
          borderRight: `1px solid ${theme.palette.divider}`,
          textAlign: 'center',
          position: 'sticky',
          left: 0,
          zIndex: 11,
          backgroundColor: theme.palette.background.paper
        }}>
          <Typography variant="subtitle2">Time</Typography>
        </Box>

        {/* Assignee column headers */}
        {filteredAssignees.map((assignee) => (
          <Box key={assignee._id} sx={{
            ...(filteredAssignees.length <= 5 ? {
              flex: 1, // Evenly distribute when 5 or fewer assignees
            } : {
              width: 150, // Fixed width when many assignees
              minWidth: 150, // Ensure minimum width
            }),
            p: 1,
            borderRight: `1px solid ${theme.palette.divider}`,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            position: 'relative'
          }}>
            {assignee._id === 'unassigned' ? (
              <Typography variant="subtitle2">
                {t('calendar.dailyView.unassigned')}
              </Typography>
            ) : assignee.isGroup ? (
              // Group header
              <>
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: '100%',
                  p: 1,
                  bgcolor: expandedGroups[assignee._id] ? 'rgba(25, 118, 210, 0.12)' : 'transparent',
                  borderRadius: 1,
                  position: 'relative',
                  ...(expandedGroups[assignee._id] && {
                    borderRight: '2px dashed rgba(25, 118, 210, 0.5)',
                  })
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, position: 'relative', width: '100%' }}>
                    <Avatar
                      sx={{
                        width: 40,
                        height: 40,
                        bgcolor: theme.palette.primary.main,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <GroupWorkIcon />
                    </Avatar>

                    <Typography variant="subtitle2" noWrap sx={{
                      maxWidth: 'calc(100% - 80px)',
                      fontWeight: 'bold',
                      ml: 1,
                      flex: 1
                    }}>
                      {assignee.name}
                    </Typography>

                    {/* Group expansion toggle */}
                    <IconButton
                      size="small"
                      onClick={() => handleToggleGroupExpansion(assignee._id)}
                      sx={{
                        bgcolor: expandedGroups[assignee._id] ? 'rgba(25, 118, 210, 0.15)' : 'rgba(0,0,0,0.05)',
                        '&:hover': { bgcolor: expandedGroups[assignee._id] ? 'rgba(25, 118, 210, 0.25)' : 'rgba(0,0,0,0.1)' }
                      }}
                    >
                      {expandedGroups[assignee._id] ? <ExpandLessIcon fontSize="small" /> : <ExpandMoreIcon fontSize="small" />}
                    </IconButton>
                  </Box>

                  <Chip
                    size="small"
                    label={`${assignee.members.length} ${t('calendar.dailyView.members', 'members')}`}
                    color="primary"
                    variant={expandedGroups[assignee._id] ? "default" : "outlined"}
                    sx={{ mt: 0.5 }}
                  />

                  {expandedGroups[assignee._id] ? (
                    <Box sx={{
                      mt: 0.5,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      <Typography variant="caption" sx={{ color: 'primary.main', fontWeight: 'medium' }}>
                        {t('calendar.dailyView.membersOnRight', 'Members →')}
                      </Typography>
                      <Box sx={{
                        position: 'absolute',
                        right: -10,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        width: 20,
                        height: 20,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'primary.main'
                      }}>
                        <Box sx={{
                          width: 0,
                          height: 0,
                          borderTop: '6px solid transparent',
                          borderBottom: '6px solid transparent',
                          borderLeft: '8px solid',
                          borderLeftColor: 'primary.main'
                        }} />
                      </Box>
                    </Box>
                  ) : (
                    <Typography variant="caption" sx={{ mt: 0.5, color: 'text.secondary' }}>
                      {t('calendar.dailyView.groupCollapsed', 'Group collapsed - showing combined tasks')}
                    </Typography>
                  )}

                  {/* Edit/Delete buttons for group */}
                  <Box sx={{ display: 'flex', mt: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenGroupDialog(assignee)}
                      sx={{ mr: 1 }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteGroup(assignee._id)}
                      color="error"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              </>
            ) : assignee.isNonUserAssignee ? (
              <Box sx={{
                width: '100%',
                position: 'relative',
                ...(assignee.isGroupMember && {
                  borderLeft: '2px dashed rgba(25, 118, 210, 0.5)',
                  pl: 1,
                  ml: 0.5
                })
              }}>
                {assignee.isGroupMember && (
                  <Box sx={{
                    position: 'absolute',
                    left: -10,
                    top: 10,
                    width: 20,
                    height: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'primary.main'
                  }}>
                    <Box sx={{
                      width: 0,
                      height: 0,
                      borderTop: '6px solid transparent',
                      borderBottom: '6px solid transparent',
                      borderRight: '8px solid',
                      borderRightColor: 'primary.main'
                    }} />
                  </Box>
                )}
                <Avatar
                  alt={assignee.name}
                  sx={{
                    width: 40,
                    height: 40,
                    mb: 1,
                    bgcolor: theme.palette.secondary.main,
                    ...(assignee.isGroupMember && {
                      border: '2px solid',
                      borderColor: 'primary.main'
                    })
                  }}
                >
                  {assignee.name.charAt(0)}
                </Avatar>
                <Typography variant="subtitle2" noWrap sx={{
                  maxWidth: '100%',
                  color: theme.palette.secondary.main,
                  ...(assignee.isGroupMember && {
                    position: 'relative'
                  })
                }}>
                  {assignee.name}
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
                  <Chip
                    size="small"
                    label={t('calendar.dailyView.external', 'External')}
                    color="secondary"
                    variant="outlined"
                  />
                  {assignee.isGroupMember && (
                    <Chip
                      size="small"
                      label={`${t('calendar.dailyView.memberOf', 'Member of')} ${assignee.groupName}`}
                      color="primary"
                      variant="outlined"
                    />
                  )}
                </Box>
              </Box>
            ) : (
              <Box sx={{
                width: '100%',
                position: 'relative',
                ...(assignee.isGroupMember && {
                  borderLeft: '2px dashed rgba(25, 118, 210, 0.5)',
                  pl: 1,
                  ml: 0.5
                })
              }}>
                {assignee.isGroupMember && (
                  <Box sx={{
                    position: 'absolute',
                    left: -10,
                    top: 10,
                    width: 20,
                    height: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'primary.main'
                  }}>
                    <Box sx={{
                      width: 0,
                      height: 0,
                      borderTop: '6px solid transparent',
                      borderBottom: '6px solid transparent',
                      borderRight: '8px solid',
                      borderRightColor: 'primary.main'
                    }} />
                  </Box>
                )}
                <Avatar
                  src={assignee.avatar}
                  alt={assignee.name}
                  sx={{
                    width: 40,
                    height: 40,
                    mb: 1,
                    ...(assignee.isGroupMember && {
                      border: '2px solid',
                      borderColor: 'primary.main'
                    })
                  }}
                >
                  {assignee.name.charAt(0)}
                </Avatar>
                <Typography variant="subtitle2" noWrap sx={{ maxWidth: '100%' }}>
                  {assignee.name}
                </Typography>
                {assignee.isGroupMember && (
                  <Chip
                    size="small"
                    label={`${t('calendar.dailyView.memberOf', 'Member of')} ${assignee.groupName}`}
                    color="primary"
                    variant="outlined"
                    sx={{ mt: 0.5 }}
                  />
                )}
              </Box>
            )}
          </Box>
        ))}
      </Box>

      {/* Scrollable content */}
      <Box
        ref={scrollContainerRef}
        sx={{
          flex: 1,
          display: 'flex',
          overflowY: 'auto',
          overflowX: 'auto', // Enable horizontal scrolling
          position: 'relative',
          '&::-webkit-scrollbar': {
            height: 8,
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.divider,
            borderRadius: 4,
          }
        }}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Current time indicator */}
        {getCurrentTimePosition() !== null && (
          <Box
            sx={{
              position: 'absolute',
              top: `${getCurrentTimePosition()}px`,
              left: 0,
              // Calculate width based on number of assignee columns
              // 80px for time column + (150px or flex 1) for each assignee column
              width: filteredAssignees.length <= 5
                ? '100%' // For fewer assignees that use flex layout
                : `calc(80px + ${filteredAssignees.length * 150}px)`, // For many assignees with fixed width
              height: '2px',
              backgroundColor: 'red',
              zIndex: 1000, // Higher z-index to ensure it's above all other elements
              pointerEvents: 'none', // Allow clicking through the line
              '&::before': {
                content: '""',
                position: 'absolute',
                left: '80px', // Align with the start of assignee columns
                top: '-4px',
                width: '10px',
                height: '10px',
                borderRadius: '50%',
                backgroundColor: 'red',
              }
            }}
          />
        )}

        {/* Drag info tooltip */}
        {dragInfo && (
          <Paper
            elevation={3}
            sx={{
              position: 'fixed',
              left: dragInfo.x,
              top: dragInfo.y,
              padding: '4px 8px',
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              borderRadius: 1,
              fontSize: '0.75rem',
              zIndex: 9999,
              pointerEvents: 'none',
            }}
          >
            {dragInfo.text}
          </Paper>
        )}
        {/* Time column */}
        <Box sx={{
          width: 80,
          minWidth: 80, // Ensure minimum width
          borderRight: `1px solid ${theme.palette.divider}`,
          position: 'sticky',
          left: 0,
          zIndex: 5,
          backgroundColor: theme.palette.background.paper
        }}>
          {timeSlots.map((time, index) => (
            <Box
              key={index}
              sx={{
                height: hourHeight,
                borderBottom: index < timeSlots.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Typography variant="body2">
                {format(time, 'h a')}
              </Typography>
            </Box>
          ))}
        </Box>

        {/* Assignee columns */}
        {filteredAssignees.map((assignee) => (
          <Box
            key={assignee._id}
            sx={{
              ...(filteredAssignees.length <= 5 ? {
                flex: 1, // Evenly distribute when 5 or fewer assignees
              } : {
                width: 150, // Fixed width when many assignees
                minWidth: 150, // Ensure minimum width
              }),
              borderRight: `1px solid ${theme.palette.divider}`,
              position: 'relative'
            }}
          >
            {/* Time slots */}
            {timeSlots.map((time, index) => (
              <Box
                key={index}
                sx={{
                  height: hourHeight,
                  borderBottom: index < timeSlots.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                  position: 'relative'
                }}
                onClick={() => handleCreateTask(assignee._id, time)}
              >
                {/* Add task button on hover */}
                <IconButton
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: 2,
                    right: 2,
                    opacity: 0.3,
                    '&:hover': { opacity: 1 }
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCreateTask(assignee._id, time);
                  }}
                >
                  <AddIcon fontSize="small" />
                </IconButton>
              </Box>
            ))}

            {/* Tasks */}
            {/* Tasks rendered with proper handling for overlapping items */}
            {(() => {
              const assigneeTasks = getTasksForAssignee(assignee._id);

              // Group overlapping tasks
              const taskGroups = [];

              assigneeTasks.forEach(task => {
                const taskStart = task.startTime ? new Date(task.startTime) : null;
                const taskEnd = task.softDeadline ? new Date(task.softDeadline) :
                              (task.hardDeadline ? new Date(task.hardDeadline) : null);

                if (!taskStart) return;

                // If no end time, assume 1 hour duration
                const effectiveEnd = taskEnd || new Date(taskStart.getTime() + 60 * 60 * 1000);

                // Find a group where this task overlaps with existing tasks
                let foundGroup = taskGroups.find(group => {
                  return group.some(groupTask => {
                    const groupTaskStart = groupTask.startTime ? new Date(groupTask.startTime) : null;
                    const groupTaskEnd = groupTask.softDeadline ? new Date(groupTask.softDeadline) :
                                        (groupTask.hardDeadline ? new Date(groupTask.hardDeadline) : null);

                    if (!groupTaskStart) return false;

                    // If no end time, assume 1 hour duration
                    const effectiveGroupEnd = groupTaskEnd || new Date(groupTaskStart.getTime() + 60 * 60 * 1000);

                    // Check if tasks overlap
                    return (taskStart < effectiveGroupEnd && effectiveEnd > groupTaskStart);
                  });
                });

                if (foundGroup) {
                  foundGroup.push(task);
                } else {
                  taskGroups.push([task]);
                }
              });

              // Render all tasks with proper positioning
              return assigneeTasks.map(task => {
                // Find which group this task belongs to
                const taskGroup = taskGroups.find(group => group.some(t => t._id === task._id)) || [];

                // Only pass overlapping tasks if there's more than one task in the group
                const overlappingTasks = taskGroup.length > 1 ? taskGroup : [];

                const { top, height, left, width } = getTaskPosition(task, overlappingTasks);
                const color = getTaskColor(task);

              return (
                <React.Fragment key={task._id}>
                  <Tooltip
                    title={
                      <Box sx={{ p: 0.5, maxWidth: 250 }}>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {task.name}
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          <Box
                            sx={{
                              width: 10,
                              height: 10,
                              borderRadius: '50%',
                              mr: 1,
                              bgcolor:
                                task.status === 'Completed' ? 'success.main' :
                                task.status === 'In Progress' ? 'info.main' :
                                task.status === 'Delayed' ? 'warning.main' :
                                task.status === 'Cancelled' ? 'error.main' :
                                'action.disabled'
                            }}
                          />
                          <Typography variant="caption">
                            {task.status}
                          </Typography>
                        </Box>

                        {task.startTime && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
                            <Typography variant="caption">
                              {t('calendar.dailyView.startTime')}: {format(new Date(task.startTime), 'h:mm a')}
                            </Typography>
                          </Box>
                        )}

                        {task.startTime && task.duration && task.duration !== '00:00:00' && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
                            <Typography variant="caption">
                              {t('taskForm.duration', 'Duration')}: {task.duration}
                            </Typography>
                          </Box>
                        )}

                        {task.startTime && task.duration && task.duration !== '00:00:00' && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
                            <Typography variant="caption">
                              {t('taskForm.end', 'End')}: {(() => {
                                try {
                                  const startTime = new Date(task.startTime);
                                  const [hours, minutes, seconds] = task.duration.split(':').map(Number);
                                  const endTime = new Date(startTime);
                                  endTime.setHours(endTime.getHours() + hours);
                                  endTime.setMinutes(endTime.getMinutes() + minutes);
                                  endTime.setSeconds(endTime.getSeconds() + seconds);
                                  return format(endTime, 'h:mm a');
                                } catch (error) {
                                  return '';
                                }
                              })()}
                            </Typography>
                          </Box>
                        )}

                        {task.location && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <LocationOnIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
                            <Typography variant="caption">
                              {task.location}
                            </Typography>
                          </Box>
                        )}

                        {task.details && (
                          <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: 'text.secondary' }}>
                            {task.details.length > 100 ? `${task.details.substring(0, 100)}...` : task.details}
                          </Typography>
                        )}
                      </Box>
                    }
                    arrow
                    placement="top"
                    enterDelay={500}
                    leaveDelay={0}
                    disableInteractive
                  >
                    <Paper
                      elevation={2}
                      data-task-id={task._id}
                      sx={{
                        position: 'absolute',
                        top: `${top}px`,
                        left: typeof left === 'number' ? `${left}px` : left,
                        width: width,
                        height: `${height}px`,
                        backgroundColor: color,
                        color: theme.palette.getContrastText(color),
                        p: 1,
                        overflow: 'hidden',
                        cursor: isDragging && draggedTask?._id === task._id ?
                          (dragType === 'move' ? 'grabbing' :
                          dragType === 'resize-top' ? 'row-resize' :
                          dragType === 'resize-bottom' ? 'row-resize' : 'pointer') : 'pointer',
                        zIndex: isDragging && draggedTask?._id === task._id ? 10 : 1,
                        display: 'flex',
                        flexDirection: 'column',
                        '&:hover': {
                          opacity: 0.9,
                          boxShadow: theme.shadows[4],
                          zIndex: 2
                        },
                        '&:hover::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: '10px',
                          cursor: 'row-resize',
                          backgroundColor: 'rgba(0,0,0,0.1)'
                        },
                        '&:hover::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          height: '10px',
                          cursor: 'row-resize',
                          backgroundColor: 'rgba(0,0,0,0.1)'
                        }
                      }}
                      onMouseDown={(e) => {
                        // Determine if we're dragging from the top, middle, or bottom
                        const rect = e.currentTarget.getBoundingClientRect();
                        const y = e.clientY - rect.top;

                        // For top and bottom edges, always start dragging
                        if (y < 10 || y > rect.height - 10) {
                          // Prevent default to avoid text selection during drag
                          e.preventDefault();
                          e.stopPropagation();

                          if (y < 10) {
                            // Top resize handle
                            handleTaskMouseDown(task, e, 'resize-top');
                          } else {
                            // Bottom resize handle
                            handleTaskMouseDown(task, e, 'resize-bottom');
                          }
                        } else {
                          // For the middle part, allow simple click and hold to drag
                          e.preventDefault();
                          e.stopPropagation();
                          handleTaskMouseDown(task, e, 'move');
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                        <Typography variant="subtitle2" noWrap sx={{ flex: 1 }}>
                          {task.name}
                        </Typography>
                        <IconButton
                          size="small"
                          sx={{
                            p: 0.5,
                            ml: 0.5,
                            backgroundColor: 'rgba(255,255,255,0.3)',
                            '&:hover': { backgroundColor: 'rgba(255,255,255,0.5)' }
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedTask(task);
                            setOpenTaskDialog(true);
                          }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Box>

                      {/* Only show details if there's enough height */}
                      {height >= 60 && (
                        <>
                          {task.duration && task.duration !== '00:00:00' && (
                            <Typography variant="caption" noWrap sx={{ mt: 0.5 }}>
                              {t('taskForm.duration', 'Duration')}: {task.duration}
                            </Typography>
                          )}

                          {task.startTime && task.duration && task.duration !== '00:00:00' && (
                            <Typography variant="caption" noWrap>
                              {t('taskForm.end', 'End')}: {(() => {
                                try {
                                  const startTime = new Date(task.startTime);
                                  const [hours, minutes, seconds] = task.duration.split(':').map(Number);
                                  const endTime = new Date(startTime);
                                  endTime.setHours(endTime.getHours() + hours);
                                  endTime.setMinutes(endTime.getMinutes() + minutes);
                                  endTime.setSeconds(endTime.getSeconds() + seconds);
                                  return format(endTime, 'h:mm a');
                                } catch (error) {
                                  return '';
                                }
                              })()}
                            </Typography>
                          )}

                          {task.location && (
                            <Typography variant="caption" noWrap>
                              {task.location}
                            </Typography>
                          )}

                          {task.taskType && !task.location && (
                            <Typography variant="caption" noWrap>
                              {task.taskType}
                            </Typography>
                          )}
                        </>
                      )}
                    </Paper>
                  </Tooltip>
                </React.Fragment>
              );
              });
            })()}
          </Box>
        ))}
      </Box>

      {/* Task Dialog */}
      <Dialog
        open={openTaskDialog}
        onClose={() => setOpenTaskDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {selectedTask && selectedTask._id ? t('tasks.edit', 'Edit Task') : t('tasks.createNew', 'Create New Task')}
            <IconButton onClick={() => setOpenTaskDialog(false)}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <TaskForm
            task={selectedTask}
            onSubmit={handleTaskSubmit}
            allTasks={tasks}
            assignees={users}
            eventId={tasks.length > 0 ? tasks[0].event : ''}
          />
        </DialogContent>
      </Dialog>

      {/* Mini Monthly Calendar Dialog */}
      <Dialog
        open={openMonthlyDialog}
        onClose={handleCloseMonthlyDialog}
        maxWidth="xs"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
            </Typography>
            <IconButton onClick={handleCloseMonthlyDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ p: 1 }}>
            <Grid container>
              {/* Day headers */}
              <Grid container spacing={0}>
                {dayNames.map(day => (
                  <Grid item key={day} sx={{ width: '14.28%', p: 0.5 }}>
                    <Typography variant="caption" align="center" display="block" fontWeight="bold">
                      {day}
                    </Typography>
                  </Grid>
                ))}
              </Grid>

              {/* Calendar days */}
              <Grid container spacing={0} sx={{ mt: 1 }}>
                {renderMiniCalendar()}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleToday} startIcon={<TodayIcon />}>
            {t('calendar.today', 'Today')}
          </Button>
          <Box sx={{ flex: 1 }} />
          <Button onClick={handleCloseMonthlyDialog}>
            {t('common.close', 'Close')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Group Management Dialog */}
      <Dialog
        open={openGroupDialog}
        onClose={handleCloseGroupDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {selectedGroup ? t('calendar.dailyView.editGroup', 'Edit Group') : t('calendar.dailyView.createGroup', 'Create Assignee Group')}
            <IconButton onClick={handleCloseGroupDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label={t('calendar.dailyView.groupName', 'Group Name')}
              value={newGroupName}
              onChange={(e) => setNewGroupName(e.target.value)}
              margin="normal"
              variant="outlined"
              autoFocus
            />

            <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>
              {t('calendar.dailyView.selectMembers', 'Select Group Members')}
            </Typography>

            <List sx={{
              maxHeight: 300,
              overflow: 'auto',
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1,
              mt: 1
            }}>
              {assignees.filter(a => a._id !== 'unassigned' && !a.isGroup).map((assignee) => {
                console.log('Rendering assignee in group dialog:', assignee);
                return (
                  <ListItem key={assignee._id} dense>
                    <ListItemIcon>
                      <Checkbox
                        edge="start"
                        checked={groupMemberIds.includes(assignee._id)}
                        onChange={(e) => {
                          console.log('Checkbox changed for', assignee.name, 'checked:', e.target.checked);
                          if (e.target.checked) {
                            const newMembers = [...groupMemberIds, assignee._id];
                            console.log('New members:', newMembers);
                            setGroupMemberIds(newMembers);
                          } else {
                            const newMembers = groupMemberIds.filter(id => id !== assignee._id);
                            console.log('New members after removal:', newMembers);
                            setGroupMemberIds(newMembers);
                          }
                        }}
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={assignee.name}
                      secondary={assignee.isNonUserAssignee ? t('calendar.dailyView.external', 'External') : assignee.email}
                    />
                  </ListItem>
                );
              })}
            </List>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseGroupDialog}>
            {t('common.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleCreateOrUpdateGroup}
            variant="contained"
            color="primary"
            disabled={!newGroupName.trim() || groupMemberIds.length < 2}
          >
            {selectedGroup ? t('common.update', 'Update') : t('common.create', 'Create')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Paper>
  );
};

export default DailyView;