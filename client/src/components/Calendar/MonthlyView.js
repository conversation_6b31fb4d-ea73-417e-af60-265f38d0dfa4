import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import { Box, Typography, Paper, useTheme, Grid, IconButton, Chip, Tooltip, Card, CardContent } from '@mui/material';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import EventIcon from '@mui/icons-material/Event';
import AssignmentIcon from '@mui/icons-material/Assignment';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LocationOnIcon from '@mui/icons-material/LocationOn';

const localizer = momentLocalizer(moment);

const MonthlyView = ({ tasks, onTaskClick, onDateSelect }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [events, setEvents] = useState([]);
  const [currentDate, setCurrentDate] = useState(new Date());

  useEffect(() => {
    if (tasks && tasks.length > 0) {
      const formattedEvents = tasks.map(task => {
        // Determine the date to use (prefer start time, fallback to soft deadline)
        const eventDate = task.startTime || task.softDeadline;

        // If no date is available, don't include in calendar
        if (!eventDate) return null;

        // Determine color based on status or deadline proximity
        let backgroundColor = theme.palette.primary.main; // Default color

        if (task.status === 'Completed') {
          backgroundColor = theme.palette.success.main;
        } else if (task.hardDeadline && moment().isAfter(task.hardDeadline)) {
          backgroundColor = theme.palette.error.main; // Overdue task
        } else if (task.softDeadline && moment().isAfter(task.softDeadline)) {
          backgroundColor = theme.palette.warning.main; // Past soft deadline
        }

        return {
          id: task._id,
          title: task.name,
          start: new Date(eventDate),
          end: new Date(moment(eventDate).add(1, 'hours').toDate()),
          resource: task,
          backgroundColor
        };
      }).filter(Boolean); // Remove null events (those without dates)

      setEvents(formattedEvents);
    }
  }, [tasks, theme.palette]);

  const eventStyleGetter = (event) => {
    return {
      style: {
        backgroundColor: event.backgroundColor,
        borderRadius: '4px',
        opacity: 0.9,
        color: 'white',
        border: '0px',
        display: 'block'
      }
    };
  };

  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  const handlePrevMonth = () => {
    setCurrentDate(prev => {
      const prevMonth = new Date(prev);
      prevMonth.setMonth(prev.getMonth() - 1);
      return prevMonth;
    });
  };

  const handleNextMonth = () => {
    setCurrentDate(prev => {
      const nextMonth = new Date(prev);
      nextMonth.setMonth(prev.getMonth() + 1);
      return nextMonth;
    });
  };

  const handleDateClick = (day) => {
    // Create a date at noon on the selected day to avoid timezone issues
    const selectedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day,
      12, 0, 0 // Set time to noon (12:00:00)
    );
    console.log('MonthlyView - Date clicked:', selectedDate);
    onDateSelect(selectedDate);
  };

  // Get events for a specific day
  const getEventsForDay = (day) => {
    const date = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );

    return events.filter(event => {
      const eventDate = new Date(event.date);
      return (
        eventDate.getDate() === date.getDate() &&
        eventDate.getMonth() === date.getMonth() &&
        eventDate.getFullYear() === date.getFullYear()
      );
    });
  };

  // Get tasks for a specific day
  const getTasksForDay = (day) => {
    const date = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );

    return tasks.filter(task => {
      // Check if the task has a soft deadline or hard deadline on this day
      const softDeadline = task.softDeadline ? new Date(task.softDeadline) : null;
      const hardDeadline = task.hardDeadline ? new Date(task.hardDeadline) : null;
      const startTime = task.startTime ? new Date(task.startTime) : null;

      const isSameDay = (d1, d2) => {
        if (!d1 || !d2) return false;
        return (
          d1.getDate() === d2.getDate() &&
          d1.getMonth() === d2.getMonth() &&
          d1.getFullYear() === d2.getFullYear()
        );
      };

      return (
        isSameDay(softDeadline, date) ||
        isSameDay(hardDeadline, date) ||
        isSameDay(startTime, date)
      );
    });
  };

  // Format task time for display
  const formatTaskTime = (task) => {
    if (task.startTime) {
      return moment(task.startTime).format('h:mm A');
    } else if (task.softDeadline) {
      return moment(task.softDeadline).format('h:mm A');
    } else if (task.hardDeadline) {
      return moment(task.hardDeadline).format('h:mm A');
    }
    return '';
  };

  // Create detailed tooltip content for tasks
  const createTaskTooltip = (task) => {
    const timeDisplay = formatTaskTime(task);

    return (
      <Box sx={{ p: 0.5, maxWidth: 250 }}>
        <Typography variant="subtitle2" fontWeight="bold">
          {task.name}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
          <Box
            sx={{
              width: 10,
              height: 10,
              borderRadius: '50%',
              mr: 1,
              bgcolor:
                task.status === 'Completed' ? 'success.main' :
                task.status === 'In Progress' ? 'info.main' :
                task.status === 'Delayed' ? 'warning.main' :
                task.status === 'Cancelled' ? 'error.main' :
                'action.disabled'
            }}
          />
          <Typography variant="caption">
            {task.status}
          </Typography>
        </Box>

        {timeDisplay && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <AccessTimeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
            <Typography variant="caption">
              {timeDisplay}
              {task.duration && task.duration !== '00:00:00' && ` (${task.duration})`}
            </Typography>
          </Box>
        )}

        {task.location && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <LocationOnIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.875rem' }} />
            <Typography variant="caption">
              {task.location}
            </Typography>
          </Box>
        )}

        {task.details && (
          <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: 'text.secondary' }}>
            {task.details.length > 100 ? `${task.details.substring(0, 100)}...` : task.details}
          </Typography>
        )}
      </Box>
    );
  };

  const renderCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDay = getFirstDayOfMonth(year, month);

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<Grid item xs={1.7} key={`empty-${i}`} />);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const eventsForDay = getEventsForDay(day);
      const tasksForDay = getTasksForDay(day);
      const isToday =
        day === new Date().getDate() &&
        month === new Date().getMonth() &&
        year === new Date().getFullYear();

      days.push(
        <Grid item xs={1.7} key={day}>
          <Paper
            sx={{
              p: 1,
              height: '100px',
              overflow: 'auto',
              cursor: 'pointer',
              bgcolor: isToday ? 'primary.light' : 'background.paper',
              color: isToday ? 'primary.contrastText' : 'text.primary',
              '&:hover': {
                bgcolor: 'action.hover'
              }
            }}
            onClick={() => handleDateClick(day)}
          >
            <Typography variant="subtitle2" fontWeight={isToday ? 'bold' : 'normal'}>
              {day}
            </Typography>

            {eventsForDay.length > 0 && (
              <Box sx={{ mt: 0.5 }}>
                {eventsForDay.map(event => (
                  <Tooltip title={event.title} key={event._id}>
                    <Chip
                      icon={<EventIcon />}
                      label={event.title.substring(0, 10) + (event.title.length > 10 ? '...' : '')}
                      size="small"
                      color="primary"
                      sx={{ mb: 0.5, maxWidth: '100%' }}
                    />
                  </Tooltip>
                ))}
              </Box>
            )}

            {tasksForDay.length > 0 && (
              <Box sx={{ mt: 0.5 }}>
                {tasksForDay.slice(0, 2).map(task => (
                  <Tooltip
                    title={createTaskTooltip(task)}
                    key={task._id}
                    arrow
                    placement="top"
                  >
                    <Chip
                      icon={<AssignmentIcon />}
                      label={task.name.substring(0, 10) + (task.name.length > 10 ? '...' : '')}
                      size="small"
                      color={
                        task.status === 'Completed' ? 'success' :
                        task.status === 'In Progress' ? 'info' :
                        task.status === 'Delayed' ? 'warning' :
                        task.status === 'Cancelled' ? 'error' :
                        'default'
                      }
                      sx={{ mb: 0.5, maxWidth: '100%' }}
                    />
                  </Tooltip>
                ))}

                {tasksForDay.length > 2 && (
                  <Typography variant="caption" color="text.secondary">
                    +{tasksForDay.length - 2} {t('common.more', 'more')}
                  </Typography>
                )}
              </Box>
            )}
          </Paper>
        </Grid>
      );
    }

    return days;
  };

  const monthNames = [
    t('calendar.months.january', 'January'),
    t('calendar.months.february', 'February'),
    t('calendar.months.march', 'March'),
    t('calendar.months.april', 'April'),
    t('calendar.months.may', 'May'),
    t('calendar.months.june', 'June'),
    t('calendar.months.july', 'July'),
    t('calendar.months.august', 'August'),
    t('calendar.months.september', 'September'),
    t('calendar.months.october', 'October'),
    t('calendar.months.november', 'November'),
    t('calendar.months.december', 'December')
  ];

  const dayNames = [
    t('calendar.days.sun', 'Sun'),
    t('calendar.days.mon', 'Mon'),
    t('calendar.days.tue', 'Tue'),
    t('calendar.days.wed', 'Wed'),
    t('calendar.days.thu', 'Thu'),
    t('calendar.days.fri', 'Fri'),
    t('calendar.days.sat', 'Sat')
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handlePrevMonth}>
          <ArrowBackIcon />
        </IconButton>

        <Typography variant="h5">
          {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
        </Typography>

        <IconButton onClick={handleNextMonth}>
          <ArrowForwardIcon />
        </IconButton>
      </Box>

      <Grid container spacing={1}>
        {dayNames.map(day => (
          <Grid item xs={1.7} key={day}>
            <Typography variant="subtitle1" align="center" fontWeight="bold">
              {day}
            </Typography>
          </Grid>
        ))}

        {renderCalendar()}
      </Grid>
    </Box>
  );
};

export default MonthlyView;